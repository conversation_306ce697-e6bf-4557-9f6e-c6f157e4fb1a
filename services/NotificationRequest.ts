import config from "@/config";
import BaseRootRequest from "./BaseRequest";

export default class NotificationRequest extends BaseRootRequest {
  getUrlPrefix() {
    return config.apiUrl;
  }

  getNotification(params: any) {
    const url = `/v1/notification`;
    return this.get(url, params);
  }

  getNotificationTransaction(params: any) {
    const url = `/v1/notification/transaction`;
    return this.get(url, params);
  }

  getNotificationAccount(params: any) {
    const url = `/v1/notification/account`;
    return this.get(url, params);
  }

  settingsNotification(params: any) {
    const url = `/v1/notification/preference`;
    return this.patch(url, params);
  }

  getSettingsNotification() {
    const url = `/v1/notification/preference`;
    return this.get(url);
  }
}
