import AuthRequest from "./AuthRequest";
import CandleRequest from "./CandleRequest";
import OrderbookRequest from "./OrderbookRequest";
import OrderRequest from "./OrderRequest";
import PairRequest from "./PairRequest";
import AccountRequest from "./AccountRequest";
import MarketDataRequest from "./MarketDataRequest";
import TradeRequest from "./TradeRequest";
import TickerRequest from "./TickerRequest";
import KYCRequest from "./KYCRequest";
import ApiManagementRequest from "./ApiManagementRequest";
import WhiteListRequest from "./WhiteListRequest";
import SecurityCheckupRequest from "./SecurityCheckupRequest";
import NotificationRequest from "./NotificationRequest";
import LoginMethodRequest from "./LoginMethodRequest";

const requestMap = {
  CandleRequest,
  AuthRequest,
  OrderbookRequest,
  OrderRequest,
  PairRequest,
  AccountRequest,
  MarketDataRequest,
  TradeRequest,
  TickerRequest,
  KYCRequest,
  ApiManagementRequest,
  WhiteListRequest,
  SecurityCheckupRequest,
  NotificationRequest,
  LoginMethodRequest,
};

const instances: Partial<
  Record<RequestKey, InstanceType<RequestMap[RequestKey]>>
> = {};

type RequestMap = typeof requestMap;

type RequestKey = keyof RequestMap;

export default class RequestFactory {
  static getRequest<T extends RequestKey>(
    classname: T
  ): InstanceType<RequestMap[T]> {
    const RequestClass = requestMap[classname];
    if (!RequestClass) {
      throw new Error(`Invalid request class name: ${classname}`);
    }

    let requestInstance = instances[classname];
    if (!requestInstance) {
      requestInstance = new RequestClass();
      instances[classname] = requestInstance;
    }

    return requestInstance as InstanceType<RequestMap[T]>;
  }
}
