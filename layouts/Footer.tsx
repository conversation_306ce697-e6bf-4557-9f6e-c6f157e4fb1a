"use client";

import { <PERSON><PERSON><PERSON>ead<PERSON>, BellIcon, SupportIcon } from "@/assets/icons";
import Marquee from "react-fast-marquee";
import { useMultipleTickers } from "@/hooks/useTicker";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useEffect, useMemo, useState } from "react";
import { Ticker, TPairSetting } from "@/types/pair";
import AppNumber from "@/components/AppNumber";
import { getPriceStyle } from "@/utils/helper";
import BigNumber from "bignumber.js";

interface TrendingItemProps {
  ticker: Ticker;
  pairSetting?: TPairSetting;
}

const TrendingItem = ({ ticker, pairSetting }: TrendingItemProps) => {
  const priceChangePercent = ticker?.priceChangePercent || "0";
  const lastPrice = ticker?.lastPrice || "0";

  const displaySymbol = pairSetting
    ? `${pairSetting.baseAsset?.toUpperCase()}/${pairSetting.quoteAsset?.toUpperCase()}`
    : ticker.symbol;

  return (
    <div className="body-sm-medium-12 flex cursor-pointer gap-2">
      <div className="text-white-1000">{displaySymbol}</div>
      <div
        style={{ color: getPriceStyle(priceChangePercent) }}
        className="flex gap-1"
      >
        <AppNumber
          value={priceChangePercent}
          decimals={2}
          isFormatLargeNumber={false}
        />
        %
      </div>
      <div className="text-white-500">
        <AppNumber
          value={lastPrice}
          decimals={pairSetting?.pricePrecision || 8}
          isFormatLargeNumber={false}
        />
      </div>
    </div>
  );
};

const MINIMUM_TICKERS_TO_SHOW = 10;

const Trending = () => {
  const { tickers } = useMultipleTickers();
  const { activePairSettings } = useSelector(
    (state: RootState) => state.pairSettings
  );

  // Get top 20 trending tickers sorted by 24h volume
  const trendingTickers = useMemo(() => {
    if (!tickers || Object.keys(tickers).length === 0) {
      return [];
    }

    const tickerArray = Object.values(tickers).filter(
      (ticker) =>
        ticker.symbol &&
        ticker.lastPrice &&
        ticker.quoteVolume &&
        BigNumber(ticker.quoteVolume).isGreaterThan(0)
    );

    // Sort by 24h volume (descending) to get most active pairs
    const sortedTickers = tickerArray.sort((a, b) => {
      return BigNumber(b.quoteVolume || 0)
        .minus(a.quoteVolume || 0)
        .toNumber();
    });

    return sortedTickers.slice(0, 20);
  }, [JSON.stringify(tickers)]);

  return (
    <Marquee
      pauseOnHover
      style={{
        height: "100%",
        width: "calc(100vw - 700px)",
      }}
    >
      {trendingTickers.length > MINIMUM_TICKERS_TO_SHOW && (
        <div className="mr-4 flex gap-4">
          {trendingTickers.map((ticker) => {
            // Find corresponding pair setting for proper formatting
            const pairSetting = activePairSettings?.find(
              (setting) => setting.symbol === ticker.symbol
            );

            return (
              <TrendingItem
                key={ticker.symbol}
                ticker={ticker}
                pairSetting={pairSetting}
              />
            );
          })}
        </div>
      )}
    </Marquee>
  );
};

export const Footer = () => {
  return (
    <div
      className="z-99 border-white-100 bg-black-50 fixed bottom-0 left-0 right-0 hidden gap-6 border-t px-4 py-2 md:flex"
      style={{
        backdropFilter: "blur(calc(var(--24, 24px) / 2))",
      }}
    >
      <div className="body-md-medium-14 flex flex items-center gap-2 text-green-500">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
        >
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M3.64905 12.2454V13.0171C3.64905 13.6533 3.12779 14.1746 2.49075 14.1746C1.85371 14.1746 1.33325 13.6533 1.33325 13.0171V12.2454C1.33325 11.6084 1.85451 11.0871 2.49075 11.0871C3.12698 11.0871 3.64905 11.6084 3.64905 12.2454ZM6.16407 8.00043C5.52706 8.00043 5.00577 8.52092 5.00577 9.15793V13.0171C5.00577 13.6533 5.52703 14.1746 6.16407 14.1746C6.80112 14.1746 7.32157 13.6533 7.32157 13.0171V9.15793C7.32157 8.52092 6.80031 8.00043 6.16407 8.00043ZM9.8366 4.91298C9.19958 4.91298 8.6791 5.43347 8.6791 6.07048V13.0171C8.6791 13.6533 9.19958 14.1746 9.8366 14.1746C10.4736 14.1746 10.9941 13.6533 10.9941 13.0171V6.07048C10.9941 5.43347 10.4736 4.91298 9.8366 4.91298ZM13.5091 1.8255C12.8721 1.8255 12.3516 2.34676 12.3516 2.983V13.0171C12.3516 13.6533 12.8721 14.1746 13.5091 14.1746C14.1461 14.1746 14.6666 13.6533 14.6666 13.0171V2.98302C14.6666 2.34679 14.1461 1.8255 13.5091 1.8255Z"
            fill="#2DBD85"
          />
        </svg>{" "}
        Stable Connection
      </div>
      <div className="border-white-100 flex-1 border-x px-6">
        <Trending />
      </div>

      <div className="flex items-center gap-6">
        <a
          href="/notification/announcement"
          className="body-md-regular-14 flex cursor-pointer items-center gap-2"
        >
          Announcements
        </a>
        {/* <div className="body-md-regular-14 flex cursor-pointer items-center gap-2">
          Cookie Preference
        </div>
        <div className="body-md-regular-14 flex cursor-pointer items-center gap-2">
          Online Support
        </div> */}
      </div>
    </div>
  );
};
