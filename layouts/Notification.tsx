"use client";

import React, { useEffect, useState } from "react";
import { AppPopover } from "@/components";
import Link from "next/link";
import {
  ProfileIcon,
  ChevronDownIcon,
  BellIcon,
  AnnouncementIcon,
  GiftIcon,
  EmailIcon20,
} from "@/assets/icons";
import {
  AppBroadcast,
  TBroadcastEvent,
  BROADCAST_EVENTS,
} from "@/libs/broadcast";
import { formatUnixTimestamp } from "@/utils/format";
import { useMediaQuery } from "react-responsive";
import { useRouter } from "next/navigation";

const NOTIFICATION_TYPE = [
  {
    link: "/notification/announcement",
    title: "Announcement",
    icon: <AnnouncementIcon />,
    type: "ANNOUNCEMENT",
  },
  {
    link: "/notification/campaign",
    title: "Campaign",
    icon: <GiftIcon />,
    type: "CAMPAIGN",
  },
  {
    link: "/notification/marketing-transaction",
    title: "Transaction",
    icon: <EmailIcon20 />,
    type: "TRANSACTION",
  },
  {
    link: "/notification/account-assistant",
    title: "Account",
    icon: <ProfileIcon />,
    type: "ACCOUNT_ASSISTANT",
  },
];

const NotificationItem = ({
  notification,
  newNotification,
}: {
  notification: any;
  newNotification: any;
}) => {
  return (
    <Link href={notification.link}>
      <div className="flex justify-between gap-4 py-2">
        <div className="flex items-center gap-2">
          {notification.icon}
          <div>
            <div className="body-md-semibold-14">{notification.title}</div>
            <div className="body-sm-regular-12 text-white-500 max-w-[222px] truncate">
              {newNotification?.m ? newNotification?.m : "No news to report"}
            </div>
          </div>
        </div>
        {newNotification && (
          <div className="body-sm-regular-12 text-white-500">
            {formatUnixTimestamp(newNotification?.ts * 1000, "DD-MM")}
          </div>
        )}
      </div>
    </Link>
  );
};

export const Notification = () => {
  const [isShow, setIsShow] = useState<boolean>(false);
  const [notifying, setNotifying] = useState(false);
  const [newNotification, setNewNotification] = useState<any>(null);
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
  const router = useRouter();

  useEffect(() => {
    const handleNewNotification = (event: TBroadcastEvent) => {
      const data: any = JSON.parse(event.detail);
      setNotifying(true);
      setNewNotification(data);
    };

    AppBroadcast.on(BROADCAST_EVENTS.NEW_NOTIFICATION, handleNewNotification);

    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.NEW_NOTIFICATION,
        handleNewNotification
      );
    };
  }, []);

  return (
    <AppPopover
      position="left"
      trigger={
        <div className="text-white-800 hover:text-white-1000 relative hidden cursor-pointer p-2 lg:block">
          <BellIcon />
          <span
            className={`absolute right-0 top-0.5 z-10 h-2 w-2 rounded-full bg-orange-400 ${
              !notifying ? "hidden" : "flex"
            }`}
          >
            <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-orange-400 opacity-75"></span>
          </span>
        </div>
      }
      content={
        <div
          style={{
            boxShadow: "4px 4px 8px 0px var(--Black-500, rgba(8, 9, 12, 0.50))",
            background:
              "linear-gradient(0deg, var(--White-100, rgba(255, 255, 255, 0.10)) 0%, var(--White-100, rgba(255, 255, 255, 0.10)) 100%), var(--Black-900, #08090C)",
          }}
          className="z-9999 border-white-100 min-w-[370px] rounded-[12px] border px-4 py-3"
        >
          <div className="border-white-100 flex items-center justify-between border-b pb-2">
            <div className="heading-sm-medium-16">Notification</div>
            <Link href="/notification/announcement">
              <div className="body-md-regular-14 flex items-center gap-4">
                View All <ChevronDownIcon className="rotate-[-90deg]" />
              </div>
            </Link>
          </div>

          {NOTIFICATION_TYPE.map((item: any, index: number) => {
            let newNotificationByType = null;
            if (item.type === newNotification?.t) {
              newNotificationByType = newNotification;
            }
            return (
              <NotificationItem
                notification={item}
                key={index}
                newNotification={newNotificationByType}
              />
            );
          })}
        </div>
      }
      isOpen={isShow}
      onToggle={() => {
        if (isMobile) {
          router.push("/notification");
          setNotifying(false);
          return;
        }
        setIsShow(!isShow);
        setNotifying(false);
      }}
      onClose={() => setIsShow(false)}
    />
  );
};
