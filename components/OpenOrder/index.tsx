"use client";

import React, { memo, useMemo, useRef, useState, useCallback } from "react";
import { CheckboxIcon, CheckboxCheckedIcon } from "@/assets/icons";
import {
  AppButtonSort,
  AppButton,
  AppSelectFilter,
  AppDataTableRealtime,
} from "@/components";
import { useMediaQuery } from "react-responsive";
import { useWindowSize } from "@/hooks";
import { ModalConfirm } from "@/modals/ModalConfirm";
import { SelectSideOrder } from "../OrderExchange/components/SelectSideOrder";
import { OpenOrderItems } from "./components/OpenOrderItems";
import rf from "@/services/RequestFactory";
import { EOrderStatus, TOpenOrder } from "@/types/order";
import { errorMsg, successMsg } from "@/libs/toast";
import { EOrderSideParam, OPTIONS_SIDE } from "../OrderHistory";
import {
  SelectOrderType,
  EOrderTypeParam,
  OPTIONS_ORDER_TYPE_OPEN_ORDER,
} from "../OrderExchange/components/SelectOrderType";
import { BROADCAST_EVENTS } from "@/libs/broadcast";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import { EOrderType } from "../OrderForm";
import { TYPE_LAYOUT } from "@/constants/common";
import {
  ButtonSelectTypeOrderMobile,
  ButtonSelectOrderSideMobile,
  ButtonSelectPairMobile,
} from "../OrderExchange/components";

export const TableOpenOrder = memo(
  ({ isInPair = false }: { isInPair?: boolean }) => {
    const [sortBy, setSortBy] = useState<string>("");
    const [side, setSide] = useState<string>(EOrderSideParam.All);
    const [orderType, setOrderType] = useState<EOrderTypeParam>(
      EOrderTypeParam.All
    );
    const [sortType, setSortType] = useState<string>("");
    const [isHideOtherPair, setIsHideOtherPair] = useState<boolean>(false);
    const [isShowModalConfirmCancelOrder, setIsShowModalConfirmCancelOrder] =
      useState<boolean>(false);

    const [pairSelected, setPairSelected] = useState<string>("");

    const dataTableRef = useRef<HTMLDivElement | null>(null);
    const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
    const { windowHeight } = useWindowSize();
    const { type } = useSelector(
      (state: RootState) => state.metadata.settingsLayout
    );
    const userInfo = useSelector((state: RootState) => state.user.userInfo);

    const isLayoutAdvanced = type === TYPE_LAYOUT.ADVANCED;

    // Get pair settings from store
    const { activePairSettings } = useSelector(
      (state: RootState) => state.pairSettings
    );

    const pairsOptions = useMemo(() => {
      const symbols: { label: string; value: string }[] =
        activePairSettings?.map(
          (item: {
            quoteAsset: string;
            baseAsset: string;
            symbol: string;
          }) => ({
            label: `${item.baseAsset}/${item.quoteAsset}`,
            value: item.symbol,
          })
        );

      return [{ label: "All", value: "" }, ...symbols];
    }, [activePairSettings]);

    const getData = useCallback(
      async (params: any) => {
        if (!userInfo.id) {
          return {
            cursor: "",
            data: [],
          };
        }

        const filterParams = {
          ...params,
          side: side || EOrderSideParam.All,
        };

        if (sortBy) {
          filterParams.sort_by = sortBy;
        }

        if (sortType) {
          filterParams.order = sortType;
        }

        if (pairSelected) {
          filterParams.symbol = pairSelected;
        }

        try {
          const { docs, cursor } = await rf
            .getRequest("OrderRequest")
            .getOpenOrders({
              ...filterParams,
              type: orderType,
            });

          return {
            cursor,
            data: docs || [],
          };
        } catch (err) {
          console.log(err, "getData error");
          return { data: [], cursor: null };
        }
      },
      [side, orderType, sortBy, sortType, pairSelected, userInfo?.id]
    );

    const tableHeight = useMemo(() => {
      if (isInPair) {
        if (isLayoutAdvanced) {
          return 450;
        }
        if (windowHeight > 1310) {
          return windowHeight - 1010;
        }
        return 300;
      }
      if (isMobile) {
        return windowHeight - 50 - 40 - 36;
      }
      return windowHeight - 250;
    }, [windowHeight, isMobile, isInPair]);

    const handleShowCancelAllModal = useCallback(() => {
      setIsShowModalConfirmCancelOrder(true);
    }, []);

    const handleCancelAllOrder = useCallback(async () => {
      try {
        await rf.getRequest("OrderRequest").cancelAllOpenOrders();

        successMsg("Cancel order successfully");
        (dataTableRef.current as any)?.removeAll();
        setIsShowModalConfirmCancelOrder(false);
      } catch (err: any) {
        errorMsg(`Cancel all orders failed with error: ${err?.message}`);
        console.log(err, "cancel all order error");
      }
    }, []);

    const onConfirmCancelOrder = useCallback(async (orderId: string) => {
      try {
        await rf.getRequest("OrderRequest").cancelOpenOrder(orderId);

        successMsg("Cancel order successfully");
      } catch (err: any) {
        errorMsg(`Cancel order failed with error: ${err?.message}`);
        console.log(err, "cancel order error");
      }
    }, []);

    const handleFormatCreateOrUpdateItem = useCallback((data: TOpenOrder) => {
      if (!data || !data.status || !data.order_id) {
        return null;
      }

      // Handle canceled or filled orders
      if (
        [EOrderStatus.CANCELLED, EOrderStatus.FILLED].includes(data.status) ||
        data.canceled_at
      ) {
        try {
          if (dataTableRef.current) {
            (dataTableRef.current as any).removeItem("order_id", data.order_id);
          }
        } catch (error) {
          console.error("Error removing item:", error);
        }
        return null;
      }

      return data;
    }, []);

    const handleCreateOrUpdateLogic = useCallback(
      (oldData: TOpenOrder, newData: TOpenOrder) => {
        const sameId = oldData.order_id === newData.order_id;

        if (!sameId) {
          return;
        }

        const oldOrderIsStopOrder = [
          EOrderType.STOP_LIMIT,
          EOrderType.STOP_MARKET,
        ].includes(oldData.type);
        const newOrderIsNormalOrder = [
          EOrderType.LIMIT,
          EOrderType.MARKET,
        ].includes(newData.type);

        // Stop order updated
        if (oldOrderIsStopOrder && newOrderIsNormalOrder) {
          return {
            ...newData,
            type: oldData.type,
            stop_price: oldData.stop_price,
            stop_condition: oldData.stop_condition,
            triggered_at: Date.now(),
          };
        }

        if (newData.status !== EOrderStatus.PARTIALLY_FILLED) {
          return null;
        }

        return {
          ...newData,
        };
      },
      []
    );

    const handleCreateOrUpdateItem = useMemo(
      () => ({
        broadcastName: BROADCAST_EVENTS.ORDER_UPDATED,
        fieldKey: "order_id",
        formatter: handleFormatCreateOrUpdateItem,
        createOrUpdate: handleCreateOrUpdateLogic,
      }),
      [handleFormatCreateOrUpdateItem, handleCreateOrUpdateLogic]
    );

    const renderHeader = useCallback(() => {
      if (isMobile) {
        return null;
      }
      return (
        <div className="flex w-full items-center">
          <div className="body-sm-regular-12 text-white-500 flex w-[11%] min-w-[90px] items-center px-2 py-1.5 ">
            Date
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[11%] min-w-[80px] px-2 py-1.5 text-left">
            Pair
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[10%] min-w-[80px] px-2 py-1.5 text-left">
            <SelectOrderType
              isOpenOrder
              orderType={orderType}
              setOrderType={(value: string) =>
                setOrderType(value as EOrderTypeParam)
              }
            />
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[10%] min-w-[70px] px-2 py-1.5 text-left">
            <SelectSideOrder
              side={side}
              setSide={(value: string) => setSide(value)}
            />
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[10%] min-w-[80px] px-2 py-1.5 text-left">
            <div className="flex items-center gap-2">
              Price
              <AppButtonSort
                value="Price"
                sortBy={sortBy}
                sortType={sortType}
                setSortType={setSortType}
                setSortBy={setSortBy}
              />
            </div>
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[10%] min-w-[80px] px-2 py-1.5 text-left">
            Amount
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[10%] min-w-[80px] px-2 py-1.5 text-left">
            Filled
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[10%] min-w-[80px] px-2 py-1.5 text-left">
            Total
          </div>
          <div className="body-sm-regular-12 text-white-500 w-[10%] min-w-[120px] px-2 py-1.5 text-center">
            Trigger Condition
          </div>
          <div className="body-sm-regular-12 text-brand-500 w-[8%] min-w-[80px] px-2 py-1.5">
            <div className="flex justify-center">
              <div
                className="body-sm-medium-12 text-brand-500 flex cursor-pointer items-center gap-1"
                onClick={handleShowCancelAllModal}
              >
                Cancel All
              </div>
            </div>
          </div>
        </div>
      );
    }, [
      isMobile,
      side,
      orderType,
      sortBy,
      sortType,
      setSortBy,
      setSortType,
      handleShowCancelAllModal,
    ]);

    const renderRow = useCallback(
      (item: TOpenOrder, index: number) => {
        return (
          <OpenOrderItems
            key={index}
            isInPair={isInPair}
            order={item}
            onConfirmCancelOrder={onConfirmCancelOrder}
          />
        );
      },
      [isInPair, onConfirmCancelOrder]
    );

    return (
      <div>
        {isInPair ? (
          <div className="flex items-center justify-between px-4 py-2 lg:hidden">
            <div
              className="body-md-regular-14 flex items-center gap-2"
              onClick={() => setIsHideOtherPair(!isHideOtherPair)}
            >
              {isHideOtherPair ? <CheckboxCheckedIcon /> : <CheckboxIcon />}
              Hide Other Pairs
            </div>
            <div>
              <AppButton
                variant="secondary"
                size="small"
                className="px-2 !text-[10px]"
                onClick={handleShowCancelAllModal}
              >
                Cancel All
              </AppButton>
            </div>
          </div>
        ) : (
          <>
            <div className="my-4 hidden gap-2 lg:flex">
              <div className="min-w-[120px]">
                <AppSelectFilter
                  options={OPTIONS_ORDER_TYPE_OPEN_ORDER}
                  value={orderType}
                  setValue={(value: string) =>
                    setOrderType(value as EOrderTypeParam)
                  }
                  title={"Filter"}
                />
              </div>
              <div className="min-w-[120px]">
                <AppSelectFilter
                  options={pairsOptions}
                  value={pairSelected}
                  setValue={setPairSelected}
                  title={"Pair"}
                />
              </div>
              <div className="min-w-[120px]">
                <AppSelectFilter
                  options={OPTIONS_SIDE}
                  value={side}
                  setValue={(value: string) =>
                    setSide(value as EOrderSideParam)
                  }
                  title={"Side"}
                />
              </div>
            </div>
            <div className="flex items-center justify-between gap-3 px-4 py-3 lg:hidden ">
              <div className="flex items-center gap-2">
                <ButtonSelectTypeOrderMobile
                  options={OPTIONS_ORDER_TYPE_OPEN_ORDER}
                  orderType={orderType}
                  setOrderType={setOrderType}
                />

                <ButtonSelectPairMobile
                  setPair={setPairSelected}
                  pair={pairSelected}
                  options={pairsOptions}
                />

                <ButtonSelectOrderSideMobile
                  setOrderSide={setSide}
                  side={side}
                />
              </div>

              {/*<div>*/}
              {/*  <div*/}
              {/*    className="body-sm-medium-12 flex cursor-pointer items-center gap-1 text-green-500"*/}
              {/*    onClick={handleShowCancelAllModal}*/}
              {/*  >*/}
              {/*    Cancel All*/}
              {/*  </div>*/}
              {/*</div>*/}
            </div>
          </>
        )}

        <div className="w-full">
          <AppDataTableRealtime
            minWidth={1108}
            ref={dataTableRef}
            getData={getData}
            overrideBodyClassName="w-full"
            handleCreateOrUpdateItem={handleCreateOrUpdateItem}
            renderHeader={renderHeader}
            renderRow={renderRow}
            height={tableHeight}
            minHeight={300}
          />
        </div>

        {isShowModalConfirmCancelOrder && (
          <ModalConfirm
            isOpen={isShowModalConfirmCancelOrder}
            onClose={() => setIsShowModalConfirmCancelOrder(false)}
            onConfirm={handleCancelAllOrder}
            titleAction="Confirm"
            description={"Are you sure you want to cancel all open orders?"}
          />
        )}
      </div>
    );
  }
);

TableOpenOrder.displayName = "TableOpenOrder";
