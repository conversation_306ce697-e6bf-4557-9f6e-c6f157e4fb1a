import { useState } from "react";
import { AppPopover } from "@/components";
import { ChevronDownIcon } from "@/assets/icons";
import { OPTIONS_SIDE } from "@/components/OrderHistory";
import { EOrderSideParam } from "../../OrderHistory/index";

export const SelectSideOrder = ({
  side,
  setSide,
}: {
  side: any;
  setSide: (value: any) => void;
}) => {
  const [isShowSelectSide, setShowSelectSide] = useState<boolean>(false);
  return (
    <AppPopover
      position="right"
      trigger={
        <div className="body-sm-medium-12 flex cursor-pointer items-center gap-1">
          Side <ChevronDownIcon />
        </div>
      }
      onClose={() => setShowSelectSide(false)}
      content={
        <div
          style={{
            boxShadow: "4px 4px 8px 0px var(--Black-500, rgba(8, 9, 12, 0.50))",
            background:
              "linear-gradient(0deg, var(--White-100, rgba(255, 255, 255, 0.10)) 0%, var(--White-100, rgba(255, 255, 255, 0.10)) 100%), var(--Black-900, #08090C)",
          }}
          className="flex min-w-[60px] flex-col gap-2 rounded-[6px] px-3 py-2"
        >
          {OPTIONS_SIDE.map((item) => {
            return (
              <div
                key={item.value}
                className={`body-sm-medium-12 hover:text-white-1000 cursor-pointer text-left ${
                  item.value === side ? "text-white-1000" : "text-white-500"
                }`}
                onClick={() => {
                  setSide(item.value);
                  setShowSelectSide(false);
                }}
              >
                {item.label}
              </div>
            );
          })}
        </div>
      }
      isOpen={isShowSelectSide}
      onToggle={() => setShowSelectSide(!isShowSelectSide)}
    />
  );
};
