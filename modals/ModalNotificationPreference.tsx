import React, { useEffect, useState } from "react";
import { BaseModal } from "@/modals/BaseModal";
import { CheckboxCheckedIcon, CheckboxIcon, CloseIcon24 } from "@/assets/icons";
import { AppButton } from "@/components";
import { useMediaQuery } from "react-responsive";
import rf from "@/services/RequestFactory";
import { errorMsg, successMsg } from "../libs/toast";

const OPTIONS_NOTIFICATION = [
  {
    name: "Announcement",
    value: "announcement",
  },
  {
    name: "Campaign",
    value: "campaign",
  },
  {
    name: "Account",
    value: "account",
  },
  {
    name: "Transaction",
    value: "transaction",
  },
  {
    name: "Select All",
    value: "select_all",
  },
];

export const ModalNotificationPreference = ({
  isOpen,
  onClose,
  preferences,
  fetchData,
}: {
  preferences: any;
  isOpen: boolean;
  onClose: () => void;
  fetchData: () => void;
}) => {
  const [checkedValues, setCheckedValues] = useState<any>({
    announcement: false,
    campaign: false,
    transaction: false,
    account: false,
    select_all: false,
  });
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  useEffect(() => {
    const allSelected = Object.values(preferences).every(Boolean);
    setCheckedValues((prev: any) => ({
      ...prev,
      ...preferences,
      select_all: allSelected,
    }));
  }, [preferences]);

  const handleChange = (key: string) => {
    if (key === "select_all") {
      const newValue = !checkedValues.select_all;
      setCheckedValues({
        announcement: newValue,
        campaign: newValue,
        transaction: newValue,
        account: newValue,
        select_all: newValue,
      });
    } else {
      const newState = {
        ...checkedValues,
        [key]: !checkedValues[key],
      };

      newState.select_all =
        newState.announcement &&
        newState.campaign &&
        newState.transaction &&
        newState.account;

      setCheckedValues(newState);
    }
  };

  const settingPreferenceNotification = async () => {
    try {
      await rf.getRequest("NotificationRequest").settingsNotification({
        preferences: {
          ...checkedValues,
        },
      });
      successMsg("Setting successfully!");
      fetchData();
      onClose();
    } catch (error: any) {
      errorMsg(error?.message || "Something went wrong!");
      console.error(error?.message || "Error Preference Notification");
    }
  };

  return (
    <BaseModal
      className="w-screen md:w-[500px]"
      isOpen={isOpen}
      onClose={onClose}
      isBottom={isMobile}
    >
      <div className=" flex justify-between">
        <div className="heading-md-semibold-18">Notification Preference</div>
        <div onClick={onClose} className="cursor-pointer">
          <CloseIcon24 />
        </div>
      </div>

      <div className="mt-7 flex flex-col gap-4">
        {OPTIONS_NOTIFICATION.map((item, index) => {
          return (
            <div
              key={index}
              className="flex cursor-pointer items-center gap-2"
              onClick={() => handleChange(item.value)}
            >
              <div>
                {checkedValues[item.value] ? (
                  <CheckboxCheckedIcon />
                ) : (
                  <CheckboxIcon />
                )}
              </div>
              <div className="body-md-regular-14">{item.name}</div>
            </div>
          );
        })}
      </div>

      <div className="mt-8 grid grid-cols-2 gap-3">
        <AppButton variant="secondary" size="large" onClick={onClose}>
          Cancel
        </AppButton>
        <AppButton
          variant="buy"
          size="large"
          onClick={settingPreferenceNotification}
        >
          Confirm
        </AppButton>
      </div>
    </BaseModal>
  );
};
