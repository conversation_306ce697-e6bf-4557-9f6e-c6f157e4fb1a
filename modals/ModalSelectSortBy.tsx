import React from "react";
import { BaseModal } from "@/modals/BaseModal";

export const OPTIONS_SORT_By = [
  {
    label: "Order Time",
    value: "createdAt",
  },
  {
    label: "Update Time",
    value: "updatedAt",
  },
];

export const ModalSelectSortBy = ({
  isOpen,
  onClose,
  sortBy,
  setSortBy,
}: {
  isOpen: boolean;
  onClose: () => void;
  setSortBy: (value: string) => void;
  sortBy: string;
}) => {
  return (
    <BaseModal
      className="w-screen md:w-[500px]"
      isOpen={isOpen}
      onClose={onClose}
      isBottom
    >
      <div className="min-h-[200px]">
        <div className="heading-sm-medium-16 mb-4">Sort By</div>

        <div className="flex flex-wrap gap-2">
          {OPTIONS_SORT_By.map((item, index) => {
            return (
              <div
                onClick={() => {
                  setSortBy(item.value);
                  onClose();
                }}
                key={index}
                className={`body-sm-medium-12 flex w-[80px] items-center justify-center rounded-[6px] border p-2 ${
                  sortBy === item.value
                    ? "border-green-800 text-green-500"
                    : "border-white-150"
                }`}
              >
                {item.label}
              </div>
            );
          })}
        </div>
      </div>
    </BaseModal>
  );
};
