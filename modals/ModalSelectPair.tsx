import React, { useEffect, useState } from "react";
import { BaseModal } from "@/modals/BaseModal";
import { CheckIcon, SearchIcon } from "@/assets/icons";

export const ModalSelectPair = ({
  isOpen,
  onClose,
  pair,
  setPair,
  options,
}: {
  isOpen: boolean;
  onClose: () => void;
  setPair: (value: string) => void;
  pair: string;
  options: any[];
}) => {
  const [search, setSearch] = useState<string>("");
  const [optionsShow, setOptionsShow] = useState<any[]>([]);

  useEffect(() => {
    let dataOptions = options;
    if (search) {
      dataOptions = dataOptions.filter((item: any) =>
        item.label?.toLowerCase().includes(search?.toLowerCase())
      );
    }

    setOptionsShow(dataOptions);
  }, [search]);

  return (
    <BaseModal
      className="w-screen md:w-[500px]"
      isOpen={isOpen}
      onClose={onClose}
      isBottom
    >
      <div className="min-h-[200px]">
        <div className="mb-2 flex items-center gap-2">
          <div className="border-white-100 flex flex-1 items-center gap-2 rounded-[6px] border p-2">
            <SearchIcon className="text-white-500" />
            <input
              onChange={(e) => setSearch(e.target.value)}
              value={search}
              placeholder="Search for symbol"
              className="body-md-regular-14 placeholder:text-white-300 flex-1 border-0 outline-none"
            />
          </div>

          <div
            className="body-md-medium-14 text-green-500"
            onClick={() => setSearch("")}
          >
            Cancel
          </div>
        </div>
        <div className="customer-scroll max-h-[300px] overflow-y-auto">
          {!!optionsShow.length ? (
            optionsShow.map((item: any, index: number) => {
              return (
                <div
                  key={index}
                  className="flex items-center justify-between py-3"
                  onClick={() => {
                    setPair(item.value);
                    onClose();
                  }}
                >
                  <div>{item.label}</div>

                  {pair === item.value && (
                    <CheckIcon className="text-green-400" />
                  )}
                </div>
              );
            })
          ) : (
            <div className="body-sm-regular-12 mt-8 text-center">
              Not found pairs
            </div>
          )}
        </div>
      </div>
    </BaseModal>
  );
};
