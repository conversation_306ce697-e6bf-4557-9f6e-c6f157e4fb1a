import React from "react";
import { BaseModal } from "@/modals/BaseModal";
import { OPTIONS_SIDE } from "@/components/OrderHistory/index";

export const ModalSelectSideOrder = ({
  isOpen,
  onClose,
  orderSide,
  setOrderSide,
}: {
  isOpen: boolean;
  onClose: () => void;
  setOrderSide: (value: string) => void;
  orderSide: string;
}) => {
  return (
    <BaseModal
      className="w-screen md:w-[500px]"
      isOpen={isOpen}
      onClose={onClose}
      isBottom
    >
      <div className="min-h-[200px]">
        <div className="heading-sm-medium-16 mb-4">Side</div>

        <div className="flex flex-wrap gap-2">
          {OPTIONS_SIDE.map((item: any, index: number) => {
            return (
              <div
                onClick={() => {
                  setOrderSide(item.value);
                  onClose();
                }}
                key={index}
                className={`body-sm-medium-12 flex w-[80px] items-center justify-center rounded-[6px] border p-2 ${
                  orderSide === item.value
                    ? "border-green-800 text-green-500"
                    : "border-white-150"
                }`}
              >
                {item.label}
              </div>
            );
          })}
        </div>
      </div>
    </BaseModal>
  );
};
