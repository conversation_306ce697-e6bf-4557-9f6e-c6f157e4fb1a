import React from "react";
import { BaseModal } from "@/modals/BaseModal";
import { CheckIcon } from "@/assets/icons";

export const ModalSelectBaseOrQuote = ({
  isOpen,
  isBase,
  onClose,
  value,
  setValue,
  options,
}: {
  isOpen: boolean;
  isBase?: boolean;
  onClose: () => void;
  setValue: (value: string) => void;
  value: string;
  options: any[];
}) => {
  return (
    <BaseModal
      className="w-screen md:w-[500px]"
      isOpen={isOpen}
      onClose={onClose}
      isBottom
    >
      <div className="min-h-[200px]">
        <div className="heading-sm-medium-16 mb-4">
          {isBase ? "Base" : "Quote"}
        </div>

        <div className="customer-scroll max-h-[300px] overflow-y-auto">
          {options.map((item: any, index: number) => {
            return (
              <div
                key={index}
                className="flex items-center justify-between py-3"
                onClick={() => {
                  setValue(item.value);
                  onClose();
                }}
              >
                <div>{item.label}</div>

                {value === item.value && (
                  <CheckIcon className="text-green-400" />
                )}
              </div>
            );
          })}
        </div>
      </div>
    </BaseModal>
  );
};
