"use client";

import React from "react";
import { BaseModal } from "./BaseModal";
import Link from "next/link";
import {
  // GlobalIcon,
  // BellIcon,
  ListIcon,
  TradeIcon,
  // BarChart,
  CoinTipIcon,
} from "@/assets/icons";
import config from "@/config/index";

interface Props {
  isOpen: boolean;
  onClose: () => void;
}

const MENU = [
  // {
  //   name: "Market",
  //   link: "#",
  //   icon: <BarChart />,
  // },
  {
    name: "Trade",
    link: "/trade/btcusdt",
    icon: <TradeIcon />,
  },
  {
    name: "Orders",
    link: "/my/orders-exchange",
    icon: <CoinTipIcon />,
  },
  {
    name: "Notification",
    link: "/notification",
    icon: (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="21"
        height="20"
        viewBox="0 0 21 20"
        fill="none"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.9296 3.125C8.0531 3.12501 5.72125 5.45686 5.72125 8.33333V11.4354C5.72125 11.7496 5.59617 12.0505 5.3745 12.2722L5.04991 12.5968C4.99778 12.6489 4.96854 12.6783 4.94713 12.7011C4.92829 12.7212 4.92543 12.7259 4.92684 12.7239C4.90838 12.7496 4.89595 12.7795 4.89068 12.8115C4.89042 12.8136 4.8896 12.8218 4.88898 12.8415C4.88799 12.8729 4.88792 12.9143 4.88792 12.9883C4.88792 13.156 4.88815 13.2559 4.89238 13.3306C4.89502 13.3773 4.89859 13.3973 4.89964 13.4024C4.92052 13.4619 4.96758 13.5091 5.02751 13.5301C5.03259 13.5311 5.05235 13.5346 5.09812 13.5372C5.17217 13.5414 5.27108 13.5417 5.43754 13.5417H16.4217C16.5881 13.5417 16.6872 13.5414 16.7614 13.5372C16.8074 13.5346 16.8273 13.5311 16.8323 13.5301C16.8913 13.5094 16.9381 13.4629 16.9593 13.4024C16.9603 13.3973 16.9639 13.3773 16.9665 13.3306C16.9708 13.2559 16.971 13.156 16.971 12.9883C16.971 12.9146 16.9709 12.8732 16.9699 12.842C16.969 12.8147 16.9677 12.8093 16.9681 12.8116C16.9626 12.7782 16.9501 12.7486 16.9325 12.724C16.9312 12.7224 16.9259 12.7159 16.9123 12.7014C16.8906 12.6784 16.8613 12.649 16.8091 12.5968L16.4845 12.2722C16.2626 12.0502 16.1379 11.7492 16.1379 11.4353V8.33333C16.1379 5.45684 13.8061 3.12499 10.9296 3.125ZM4.47125 8.33333C4.47125 4.7665 7.36275 1.87501 10.9296 1.875C14.4964 1.87499 17.3879 4.76649 17.3879 8.33333V11.4078L17.693 11.7129C17.6995 11.7194 17.706 11.7259 17.7126 11.7325C17.7919 11.8115 17.8773 11.8968 17.9477 11.9948C18.0798 12.1788 18.1655 12.3893 18.2016 12.6091C18.2213 12.7289 18.2211 12.8489 18.221 12.9629C18.221 12.9714 18.221 12.9799 18.221 12.9883C18.221 13.0103 18.2211 13.0324 18.2211 13.0546C18.2217 13.3059 18.2224 13.5687 18.1445 13.7996C17.9993 14.2302 17.6617 14.5695 17.2294 14.7153C16.9991 14.793 16.7367 14.7924 16.4875 14.7918C16.4654 14.7917 16.4435 14.7917 16.4217 14.7917H14.0546V15C14.0546 16.7259 12.6555 18.125 10.9296 18.125C9.20369 18.125 7.80458 16.7259 7.80458 15V14.7917H5.43755C5.41573 14.7917 5.39378 14.7917 5.37174 14.7918C5.12247 14.7924 4.86073 14.793 4.63038 14.7153C4.19911 14.5699 3.86002 14.2312 3.71441 13.7995C3.63654 13.5687 3.63719 13.3059 3.63781 13.0546C3.63786 13.0324 3.63792 13.0103 3.63792 12.9883C3.63792 12.9791 3.6379 12.9699 3.63789 12.9606C3.63776 12.8482 3.63761 12.7283 3.65717 12.6092C3.69348 12.3879 3.78022 12.1777 3.91149 11.9948C3.98222 11.8963 4.0673 11.8114 4.14777 11.7311C4.15388 11.725 4.15997 11.7189 4.16603 11.7129L4.47125 11.4077V8.33333ZM9.05458 14.7917V15C9.05458 16.0355 9.89405 16.875 10.9296 16.875C11.9651 16.875 12.8046 16.0355 12.8046 15V14.7917H9.05458Z"
          fill="currentColor"
        />
      </svg>
    ),
  },
  {
    name: "Docs",
    link: config.docsUrl,
    icon: <ListIcon />,
  },
  // {
  //   name: "English",
  //   link: "#",
  //   icon: <GlobalIcon />,
  // },
];

export const ModalMenu = ({ isOpen, onClose }: Props) => {
  return (
    <BaseModal isOpen={isOpen} onClose={onClose} isBottom>
      <div className="flex flex-col gap-2">
        {MENU.map((item: any, index: number) => {
          return (
            <Link
              onClick={onClose}
              href={item.link}
              key={index}
              className="text-white-500 flex items-center gap-2 py-2"
            >
              {item.icon} {item.name}
            </Link>
          );
        })}
      </div>
    </BaseModal>
  );
};
