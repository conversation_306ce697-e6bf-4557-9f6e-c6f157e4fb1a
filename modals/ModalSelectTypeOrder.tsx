import React from "react";
import { BaseModal } from "@/modals/BaseModal";
import { EOrderTypeParam } from "@/components/OrderExchange/components/SelectOrderType";

export const ModalSelectTypeOrder = ({
  isOpen,
  onClose,
  options,
  orderType,
  setOrderType,
}: {
  isOpen: boolean;
  onClose: () => void;
  options: any[];
  setOrderType: (value: EOrderTypeParam) => void;
  orderType: EOrderTypeParam;
}) => {
  return (
    <BaseModal
      className="w-screen md:w-[500px]"
      isOpen={isOpen}
      onClose={onClose}
      isBottom
    >
      <div className="min-h-[200px]">
        <div className="heading-sm-medium-16 mb-4">Order type</div>

        <div className="flex flex-wrap gap-2">
          {options.map((item, index) => {
            return (
              <div
                onClick={() => {
                  setOrderType(item.value);
                  onClose();
                }}
                key={index}
                className={`body-sm-medium-12 flex w-[80px] items-center justify-center rounded-[6px] border p-2 ${
                  orderType === item.value
                    ? "border-green-800 text-green-500"
                    : "border-white-150"
                }`}
              >
                {item.label}
              </div>
            );
          })}
        </div>
      </div>
    </BaseModal>
  );
};
