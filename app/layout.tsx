import type { Metadata, Viewport } from "next";
import "./globals.css";
import { AppProvider } from "@/app/provider";
import "rc-tooltip/assets/bootstrap.css";
import "react-datepicker/dist/react-datepicker.css";
import "tippy.js/dist/tippy.css";
import { cookies } from "next/headers";
import { COOKIES_ACCESS_TOKEN_KEY } from "@/constants";
import NextTopLoader from "nextjs-toploader";
import { BaseLayout } from "./layouts";

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  userScalable: false,
  maximumScale: 1,
  viewportFit: "cover",
  themeColor: "#000000",
};

export const metadata: Metadata = {
  title: "VDAX",
  description: "VDAX",
  icons: {
    icon: "/icons/logo-icon.svg",
    apple: "/icons/logo-icon.svg",
  },
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const accessToken =
    (await cookies()).get(COOKIES_ACCESS_TOKEN_KEY)?.value || "";

  return (
    <html lang="en">
      <body>
        <NextTopLoader
          color="#ffffff80"
          height={2}
          showSpinner={true}
          speed={200}
          shadow="0 0 10px #your-color,0 0 5px #your-color"
        />
        <AppProvider authorization={accessToken}>
          <BaseLayout>{children}</BaseLayout>
        </AppProvider>
      </body>
    </html>
  );
}
