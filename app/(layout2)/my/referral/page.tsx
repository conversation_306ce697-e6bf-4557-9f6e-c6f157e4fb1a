"use client";

import {
  CopyIcon,
  FacebookSmallIcon,
  QrSmallIcon,
  TelegramSmallIcon,
  XSmallIcon,
} from "@/assets/icons";
import config from "@/config";
import { errorMsg } from "@/libs/toast";
import { ModalReferral } from "@/modals";
import rf from "@/services/RequestFactory";
import { copyToClipboard } from "@/utils/helper";
import cx from "classnames";
import Image from "next/image";
import Link from "next/link";
import { useCallback, useEffect, useMemo, useState } from "react";

const socialIcons = (referralLink: string) => {
  return [
    {
      icon: FacebookSmallIcon,
      url: `https://www.facebook.com/sharer/sharer.php?u=${referralLink}`,
    },
    // {
    //   icon: ZaloSmallIcon,
    //   url: "https://www.vdax.com",
    // },
    {
      icon: TelegramSmallIcon,
      url: `https://t.me/share/url?url=${referralLink}&text=Sign up on the VDAX crypto exchange platform and earn a 100 USD trading fee rebate voucher!`,
    },
    {
      icon: XSmallIcon,
      url: `https://twitter.com/intent/tweet?url=${referralLink}&text=Sign up on the VDAX crypto exchange platform and earn a 100 USD trading fee rebate voucher!`,
    },
    // {
    //   icon: DiscordSmallIcon,
    //   url: "https://www.vdax.com",
    // },
  ];
};

export default function ReferralPage() {
  const [referralCode, setReferralCode] = useState<string | null>(null);
  const [isOpenReferralQR, setIsOpenReferralQR] = useState<boolean>(false);

  const getProfile = async () => {
    try {
      const result = await rf.getRequest("AccountRequest").getProfile({});
      setReferralCode(result?.referralCode || null);
    } catch (error: any) {
      errorMsg(error?.message || "Error get profile");
    }
  };

  useEffect(() => {
    getProfile();
  }, []);

  const referralLink = useMemo(
    () => `${config.appApiUrl}?ref=${referralCode}`,
    [referralCode]
  );

  const onCopyReferralCode = useCallback(() => {
    if (referralCode) {
      copyToClipboard(referralCode);
    }
  }, [referralCode]);

  const onCopyReferralLink = useCallback(() => {
    copyToClipboard(referralLink);
  }, [referralLink]);

  return (
    <div className="mb-[93.12px] flex flex-col gap-8">
      <div className="text-white-1000 hidden text-[24px] font-semibold leading-[120%] md:block">
        Referral
      </div>

      <div className="flex flex-col items-center gap-0 md:gap-[32px]">
        <div className="flex flex-col gap-[9px] px-[16px] py-[16px] md:py-0">
          <div className="text-white-1000 heading-lg-semibold-24 text-center md:text-[40px] md:font-semibold md:leading-[120%]">
            Refer a friend Both Earn $100
          </div>
          <div className="text-white-500 body-md-regular-14 text-center md:text-[18px] md:font-[400] md:leading-[120%]">
            Refer friend to deposit over $50, and both receive $100 in trading
            fee rebate voucher.{" "}
            <Link href="/" className="text-brand-500 body-md-regular-18">
              Learn more
            </Link>
          </div>
        </div>

        <div className="flex w-full max-w-[400px] flex-col items-center gap-[32px] px-[16px] md:px-0">
          <div className="relative h-[234px] w-full">
            <Image
              src={"/images/ReferralMoney.png"}
              alt="avatar"
              width={900}
              height={234}
              className="scale-120 absolute left-1/2 top-1/2 z-0 -translate-x-1/2 -translate-y-1/2"
            />
          </div>

          <div className="z-1 flex w-full flex-col gap-[8px]">
            <div className="text-white-1000 heading-sm-medium-16 w-full py-[4px]">
              Invite Via
            </div>

            <div className="flex w-full flex-col gap-[24px]">
              <div
                className={cx(
                  "flex max-h-[40px] items-center justify-between gap-2 self-stretch rounded-[12px] p-[12px]",
                  "border-white-100 bg-white-100 rounded-[6px] border"
                )}
              >
                <div className="body-sm-medium-12 text-white-1000">
                  Referral Code
                </div>
                <div
                  className={cx(
                    "body-md-regular-14 text-white-1000",
                    "flex items-center gap-2"
                  )}
                >
                  <div>{referralCode}</div>
                  <div className="cursor-pointer" onClick={onCopyReferralCode}>
                    <CopyIcon className="h-4 w-4" />
                  </div>
                </div>
              </div>

              <div
                className={cx(
                  "flex max-h-[40px] items-center justify-between gap-2 self-stretch rounded-[12px] p-[12px]",
                  "border-white-100 bg-white-100 rounded-[6px] border"
                )}
              >
                <div className="body-sm-medium-12 text-white-1000">
                  Referral Link
                </div>
                <div
                  className={cx(
                    "body-md-regular-14 text-white-1000",
                    "flex items-center gap-2"
                  )}
                >
                  <div
                    className="max-w-[200px] overflow-hidden text-ellipsis whitespace-nowrap"
                    title={referralLink}
                  >
                    {referralLink}
                  </div>
                  <div className="cursor-pointer" onClick={onCopyReferralLink}>
                    <CopyIcon className="h-4 w-4" />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="flex w-full flex-col gap-[8px]">
            <div className="text-white-1000 py-[4px] text-[16px] font-medium leading-[24px]">
              Share to
            </div>

            <div className="flex justify-between">
              {socialIcons(referralLink).map((icon) => (
                <Link
                  href={icon.url}
                  key={icon.url}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <div
                    className={cx(
                      "max-h-[40px] min-h-[40px] min-w-[40px] px-[8px] py-[10px]",
                      "bg-white-100 flex items-center justify-center rounded-lg"
                    )}
                  >
                    <icon.icon />
                  </div>
                </Link>
              ))}

              <div
                className={cx(
                  "max-h-[40px] min-h-[40px] min-w-[40px] px-[8px] py-[10px]",
                  "bg-white-100 flex cursor-pointer items-center justify-center rounded-lg"
                )}
                onClick={() => setIsOpenReferralQR(true)}
              >
                <QrSmallIcon className="h-4 w-4" />
              </div>
            </div>

            {referralCode && referralLink && (
              <ModalReferral
                isOpen={isOpenReferralQR}
                onClose={() => setIsOpenReferralQR(false)}
                referralCode={referralCode}
                referralLink={referralLink}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
