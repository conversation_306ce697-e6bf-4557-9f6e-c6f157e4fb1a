"use client";

import React, { useEffect, useState } from "react";
import { TableRecentDeposit } from "@/components/TableRecentDeposit";
import { DepositForm } from "./_parts/DepositForm";
import { useSelector } from "react-redux";
import { KYC_STATUS } from "@/constants/common";
import { RootState } from "@/store/index";
import { ModalRequiredVerifyKYC } from "@/modals";

export default function DepositPage() {
  const userInfo = useSelector((state: RootState) => state.user.userInfo);
  const [isShowModalRequiredVerifyKYC, setIsShowModalRequiredVerifyKYC] =
    useState<boolean>(false);

  useEffect(() => {
    if (!Object.keys(userInfo).length) return;
    if (userInfo.kycStatus === KYC_STATUS.UNVERIFIED) {
      setIsShowModalRequiredVerifyKYC(true);
    }
  }, [userInfo?.kycStatus]);

  return (
    <div className="flex flex-col lg:gap-8">
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        <div>
          <div className="mt-3 px-4 lg:mt-0 lg:px-0">
            <DepositForm />
          </div>
        </div>
        <div className="hidden items-start gap-8 lg:flex ">
          <div className="flex flex-1 flex-col gap-4">
            <div className="body-md-medium-14">FAQ</div>
            {/*<a href="#" target="_blank">
              <div className="body-md-regular-14">
                How to deposit crypto?(Video)
              </div>
            </a>*/}
            <a href="#" target="_blank">
              <div className="body-md-regular-14">
                How to Deposit Crypto Step-by-step Guide
              </div>
            </a>
            <a href="#" target="_blank">
              <div className="body-md-regular-14">
                Deposit hasn&apos;t arrived?
              </div>
            </a>
            <a href="#" target="_blank">
              <div className="body-md-regular-14">
                Deposit & Withdrawal Status query
              </div>
            </a>
          </div>
          {/*<div className="body-sm-medium-12 flex cursor-pointer items-center gap-2">*/}
          {/*  More <ChevronDownIcon className="rotate-[-90deg]" />*/}
          {/*</div>*/}
        </div>
      </div>
      <TableRecentDeposit />

      {isShowModalRequiredVerifyKYC && (
        <ModalRequiredVerifyKYC
          isOpen={isShowModalRequiredVerifyKYC}
          onClose={() => setIsShowModalRequiredVerifyKYC(false)}
        />
      )}
    </div>
  );
}
