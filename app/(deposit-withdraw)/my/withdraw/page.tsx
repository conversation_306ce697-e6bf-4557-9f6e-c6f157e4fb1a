"use client";

import React, { useEffect, useState } from "react";
import { TableRecentWithdraw } from "@/components/TableRecentWithdraw";
import { WithdrawForm } from "./_parts/WithdrawForm";
import { useSelector } from "react-redux";
import { ModalRequiredVerifyKYC } from "@/modals";
import { KYC_STATUS } from "@/constants/common";
import { RootState } from "@/store/index";

export default function WithdrawPage() {
  const userInfo = useSelector((state: RootState) => state.user.userInfo);
  const [isShowModalRequiredVerifyKYC, setIsShowModalRequiredVerifyKYC] =
    useState<boolean>(false);

  useEffect(() => {
    if (!Object.keys(userInfo).length) return;
    if (userInfo.kycStatus === KYC_STATUS.UNVERIFIED) {
      setIsShowModalRequiredVerifyKYC(true);
    }
  }, [userInfo?.kycStatus]);

  return (
    <div className="flex flex-col lg:gap-8">
      <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
        <div>
          <div className="mt-3 px-4 lg:mt-0 lg:px-0">
            <WithdrawForm />
          </div>
        </div>
        <div className="hidden items-start gap-8 lg:flex ">
          <div className="flex flex-1 flex-col gap-4">
            <div className="body-md-medium-14">FAQ</div>
            <a href="#" target="_blank">
              <div className="body-md-regular-14">How to withdraw crypto?</div>
            </a>
            <a href="#" target="_blank">
              <div className="body-md-regular-14">
                How to Find My Transaction ID (TxID)?
              </div>
            </a>
            <a href="#" target="_blank">
              <div className="body-md-regular-14">
                Deposit & Withdraw Status query
              </div>
            </a>
          </div>
          {/*<div className="body-sm-medium-12 flex cursor-pointer items-center gap-2">*/}
          {/*  More <ChevronDownIcon className="rotate-[-90deg]" />*/}
          {/*</div>*/}
        </div>
      </div>
      <TableRecentWithdraw />
      {isShowModalRequiredVerifyKYC && (
        <ModalRequiredVerifyKYC
          isOpen={isShowModalRequiredVerifyKYC}
          onClose={() => setIsShowModalRequiredVerifyKYC(false)}
        />
      )}
    </div>
  );
}
