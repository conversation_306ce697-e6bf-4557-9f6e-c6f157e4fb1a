"use client";
import React, { useMemo } from "react";
import { ChevronDownIcon, NotFoundIcon } from "@/assets/icons";
import Link from "next/link";
import { useWindowSize } from "@/hooks/useWindowSize";
import moment from "moment";

const CampaignList = ({ campaigns }: { campaigns: any }) => {
  const { windowHeight } = useWindowSize();

  const tableHeight = useMemo(() => {
    return windowHeight - 180;
  }, [windowHeight]);

  return (
    <div
      style={{ height: tableHeight }}
      className="customer-scroll overflow-y-auto"
    >
      {!!campaigns.length ? (
        campaigns.map((item: any, index: number) => {
          const dataCampaign = item?.attributes as any;
          return (
            <Link
              href={`/campaign/${dataCampaign?.slug}`}
              key={index}
              className="border-white-100 flex gap-3 border-b py-4 lg:px-4 lg:py-6"
            >
              <div className=" flex flex-1 flex-col justify-between gap-4 lg:flex-row lg:items-center">
                <div className="body-md-semibold-14">{dataCampaign?.title}</div>

                <div className="body-md-regular-14 text-white-500 flex items-center gap-4">
                  {moment(dataCampaign?.publishedAt).format(
                    "YYYY/MM/DD HH:mm:ss"
                  )}
                  <ChevronDownIcon className="hidden rotate-[-90deg] lg:block" />
                </div>
              </div>
            </Link>
          );
        })
      ) : (
        <div className="flex h-full flex-col items-center justify-center">
          <NotFoundIcon />
          <div className="text-white-500 body-md-regular-14">
            No records found.
          </div>
        </div>
      )}
    </div>
  );
};

export default CampaignList;
