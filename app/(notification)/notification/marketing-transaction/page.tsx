"use client";

import React, { useMemo, useRef } from "react";
import { ArrowDown, ChevronDownIcon } from "@/assets/icons";
import Link from "next/link";
import { useWindowSize } from "@/hooks/useWindowSize";
import rf from "@/services/RequestFactory";
import { AppDataTableRealtime } from "@/components";
import { BROADCAST_EVENTS } from "@/libs/broadcast";
import { formatUnixTimestamp } from "@/utils/format";
import { TNotification } from "@/types/notification";

export default function MarketingTransactionNotificationPage() {
  const dataTableRef = useRef<HTMLDivElement | null>(null);
  const { windowHeight } = useWindowSize();

  const getData = async (params: any) => {
    try {
      const res = await rf
        .getRequest("NotificationRequest")
        .getNotificationTransaction({
          ...params,
        });

      return {
        cursor: res?.cursor,
        data: res?.docs || [],
      };
    } catch (err) {
      console.log(err, "Get Notification Error");
      return { cursor: null, data: [] };
    }
  };

  const tableHeight = useMemo(() => {
    return windowHeight - 180;
  }, [windowHeight]);

  return (
    <div className="border-white-100 rounded-[16px] p-4 lg:h-[calc(100vh-100px)] lg:border">
      <div className="mb-4 block lg:hidden">
        <Link href="/notification">
          <ArrowDown className="rotate-[90deg]" />
        </Link>
      </div>

      <div className="heading-sm-medium-16 lg:text-white-500 mb-2 pb-4 text-[24px] lg:mb-0 lg:text-[16px]">
        Transaction
      </div>

      <AppDataTableRealtime
        minWidth={1300}
        ref={dataTableRef}
        getData={getData as any}
        shouldAutoFetchOnInit
        overrideBodyClassName="w-full"
        handleAddNewItem={{
          broadcastName: BROADCAST_EVENTS.NEW_NOTIFICATION,
          fieldKey: "id",
          formatter: (data: any) => {
            const newData = JSON.parse(data) as any;
            if (newData.t !== "TRANSACTION") return null;
            return {
              id: newData.i,
              message: newData.m,
              timestamp: newData.ts,
              userId: newData.u,
            };
          },
        }}
        renderHeader={() => {
          return null;
        }}
        renderRow={(item: TNotification, index: number) => {
          return (
            <div
              key={index}
              className="border-white-100 flex gap-3 border-b py-4 lg:px-4 lg:py-6"
            >
              <div className=" flex flex-1 flex-col justify-between gap-4 lg:flex-row lg:items-center">
                <div className="body-md-regular-14">{item.content}</div>

                <div className="body-md-regular-14 text-white-500 flex items-center gap-4">
                  {formatUnixTimestamp(+item?.createdAt * 1000)}
                  <ChevronDownIcon className="hidden rotate-[-90deg] lg:block" />
                </div>
              </div>
            </div>
          );
        }}
        height={tableHeight}
      />
    </div>
  );
}
